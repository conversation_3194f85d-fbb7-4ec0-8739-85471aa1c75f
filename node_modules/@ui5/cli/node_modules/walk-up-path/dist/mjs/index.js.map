{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AACvC,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,IAAY;IAC3C,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG;QAChC,MAAM,IAAI,CAAA;QACV,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,EAAE,KAAK,IAAI,EAAE;YACf,MAAK;SACN;aAAM;YACL,IAAI,GAAG,EAAE,CAAA;SACV;KACF;AACH,CAAC,CAAA", "sourcesContent": ["import { dirname, resolve } from 'path'\nexport const walkUp = function* (path: string) {\n  for (path = resolve(path); path;) {\n    yield path\n    const pp = dirname(path)\n    if (pp === path) {\n      break\n    } else {\n      path = pp\n    }\n  }\n}\n"]}