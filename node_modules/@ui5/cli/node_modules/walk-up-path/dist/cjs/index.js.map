{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;AAAA,+BAAuC;AAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,IAAY;IAC3C,KAAK,IAAI,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,EAAE,IAAI,GAAG;QAChC,MAAM,IAAI,CAAA;QACV,MAAM,EAAE,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAA;QACxB,IAAI,EAAE,KAAK,IAAI,EAAE;YACf,MAAK;SACN;aAAM;YACL,IAAI,GAAG,EAAE,CAAA;SACV;KACF;AACH,CAAC,CAAA;AAVY,QAAA,MAAM,UAUlB", "sourcesContent": ["import { dirname, resolve } from 'path'\nexport const walkUp = function* (path: string) {\n  for (path = resolve(path); path;) {\n    yield path\n    const pp = dirname(path)\n    if (pp === path) {\n      break\n    } else {\n      path = pp\n    }\n  }\n}\n"]}