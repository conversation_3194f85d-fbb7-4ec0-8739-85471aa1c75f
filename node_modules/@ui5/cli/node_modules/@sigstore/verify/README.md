# @sigstore/verify &middot; [![npm version](https://img.shields.io/npm/v/@sigstore/verify.svg?style=flat)](https://www.npmjs.com/package/@sigstore/verify) [![CI Status](https://github.com/sigstore/sigstore-js/workflows/CI/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/ci.yml) [![Smoke Test Status](https://github.com/sigstore/sigstore-js/workflows/smoke-test/badge.svg)](https://github.com/sigstore/sigstore-js/actions/workflows/smoke-test.yml)

A library for verifying [Sigstore][1] signatures.

## Prerequisites

- Node.js version >= 16.14.0

[1]: https://www.sigstore.dev
