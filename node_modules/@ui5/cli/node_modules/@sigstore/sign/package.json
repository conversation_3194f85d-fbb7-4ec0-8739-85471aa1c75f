{"name": "@sigstore/sign", "version": "2.3.2", "description": "Sigstore signing library", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "shx rm -rf dist *.tsbuildinfo", "build": "tsc --build", "test": "jest"}, "files": ["dist"], "author": "<EMAIL>", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/sigstore/sigstore-js.git"}, "bugs": {"url": "https://github.com/sigstore/sigstore-js/issues"}, "homepage": "https://github.com/sigstore/sigstore-js/tree/main/packages/sign#readme", "publishConfig": {"provenance": true}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.4", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4", "@types/promise-retry": "^1.1.6"}, "dependencies": {"@sigstore/bundle": "^2.3.2", "@sigstore/core": "^1.0.0", "@sigstore/protobuf-specs": "^0.3.2", "make-fetch-happen": "^13.0.1", "proc-log": "^4.2.0", "promise-retry": "^2.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}