{"name": "titleize", "version": "3.0.0", "description": "Capitalize every word in a string: `unicorn cake` → `Unicorn Cake`", "license": "MIT", "repository": "sindre<PERSON>hus/titleize", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["titleize", "title", "capitalize", "uppercase", "case", "dash", "hyphen", "string", "text", "convert"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}